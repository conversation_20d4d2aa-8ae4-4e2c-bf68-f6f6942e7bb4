import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import os

def visualize_results(structured_data):
    """Generate visualizations based on the structured question data."""
    if not os.path.exists('outputs/visuals'):
        os.makedirs('outputs/visuals')

    df = pd.DataFrame(structured_data)
    df['has_equation'] = df['equations'].apply(lambda x: len(x) > 0)
    df['has_table'] = df['tables'].apply(lambda x: len(x) > 0)
    df['has_figure'] = df['figures'].apply(lambda x: len(x) > 0)
    df['has_answer'] = df['answer'].apply(lambda x: len(str(x).strip()) > 0)
    df['question_length'] = df['question'].apply(len)

    # Set style for better looking plots
    plt.style.use('default')
    sns.set_palette("husl")

    # 1. Bar chart: Question types distribution (fixed label overflow)
    plt.figure(figsize=(10, 6))
    type_counts = df['type'].value_counts()

    # Create bar plot with better formatting
    ax = type_counts.plot(kind='bar', color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
    plt.title('Question Types Distribution', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Question Type', fontsize=12, fontweight='bold')
    plt.ylabel('Number of Questions', fontsize=12, fontweight='bold')

    # Fix label overflow by rotating and adjusting
    plt.xticks(rotation=45, ha='right')

    # Add value labels on bars
    for i, v in enumerate(type_counts.values):
        ax.text(i, v + 0.1, str(v), ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    plt.savefig('outputs/visuals/question_types_bar.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 2. Pie chart: Subject distribution (based on actual data)
    plt.figure(figsize=(8, 8))
    subject_counts = df['subject'].value_counts()
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']

    plt.pie(subject_counts.values, labels=subject_counts.index, autopct='%1.1f%%',
            colors=colors[:len(subject_counts)], startangle=90)
    plt.title('Subject Distribution', fontsize=16, fontweight='bold', pad=20)
    plt.axis('equal')
    plt.tight_layout()
    plt.savefig('outputs/visuals/subjects_pie.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 3. Stacked bar chart: Content features by question type
    plt.figure(figsize=(12, 6))

    # Create feature matrix
    features_by_type = df.groupby('type')[['has_equation', 'has_table', 'has_figure', 'has_answer']].sum()

    # Create stacked bar chart
    ax = features_by_type.plot(kind='bar', stacked=True,
                              color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
                              figsize=(12, 6))

    plt.title('Content Features by Question Type', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Question Type', fontsize=12, fontweight='bold')
    plt.ylabel('Number of Questions', fontsize=12, fontweight='bold')
    plt.xticks(rotation=45, ha='right')
    plt.legend(['Has Equations', 'Has Tables', 'Has Figures', 'Has Answers'],
               bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout()
    plt.savefig('outputs/visuals/features_stacked_bar.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 4. Heatmap: Question characteristics matrix
    plt.figure(figsize=(10, 8))

    # Create correlation matrix of question characteristics
    char_data = df[['has_equation', 'has_table', 'has_figure', 'has_answer', 'question_length']].copy()
    char_data['question_length'] = (char_data['question_length'] > char_data['question_length'].median()).astype(int)
    char_data.columns = ['Has Equations', 'Has Tables', 'Has Figures', 'Has Answers', 'Long Questions']

    correlation_matrix = char_data.corr()

    # Create heatmap
    sns.heatmap(correlation_matrix, annot=True, cmap='RdYlBu_r', center=0,
                square=True, fmt='.2f', cbar_kws={'shrink': 0.8})
    plt.title('Question Characteristics Correlation Matrix', fontsize=16, fontweight='bold', pad=20)
    plt.tight_layout()
    plt.savefig('outputs/visuals/characteristics_heatmap.png', dpi=300, bbox_inches='tight')
    plt.close()

    print(f"Explicit Output: Generated 4 visualizations in outputs/visuals/ based on {len(df)} questions")