import json
import re

def structure_data(raw_data):
    """Structures raw parsed data into JSON with LaTeX."""
    # First, clean and filter the raw data, properly grouping questions with answers
    cleaned_data = _clean_and_group_questions(raw_data)

    structured = []
    for item in cleaned_data:
        # Enhanced question type detection
        question_text = item["raw_question"].lower()
        answer_text = item.get("raw_answer", "").lower()

        # Determine question type with more sophisticated logic
        if (re.search(r"\([A-D]\)", item["raw_question"]) or
            re.search(r"[A-D][\.\)]\s", item["raw_question"]) or
            re.search(r"choices?", question_text) or
            re.search(r"select.*correct", question_text) or
            re.search(r"which.*following", question_text)):
            q_type = "multiple-choice"
        elif (re.search(r"true.*false|false.*true", question_text) or
              re.search(r"yes.*no|no.*yes", question_text) or
              re.search(r"true.*false", answer_text)):
            q_type = "true-false"
        elif (re.search(r"fill.*blank|complete.*sentence", question_text) or
              re.search(r"_+", item["raw_question"])):  # Blanks indicated by underscores
            q_type = "fill-in-blank"
        elif (re.search(r"match.*following|connect.*pairs", question_text)):
            q_type = "matching"
        elif (re.search(r"solve|calculate|find.*value|compute", question_text) and
              re.search(r"[\$\\].*[\$\\]", item["raw_question"])):  # Has math equations
            q_type = "calculation"
        elif (re.search(r"explain|describe|discuss|analyze|compare", question_text)):
            q_type = "essay"
        else:
            q_type = "open-ended"

        # Enhanced equation extraction
        equation_patterns = [
            r"[\$]{1,2}(.*?)[\$]{1,2}",  # LaTeX math mode
            r"\\begin\{equation\}(.*?)\\end\{equation\}",  # Equation environment
            r"\\begin\{align\}(.*?)\\end\{align\}",  # Align environment
            r"\\begin\{math\}(.*?)\\end\{math\}",  # Math environment
        ]

        equations = []
        full_text = item["raw_question"] + " " + item.get("raw_answer", "")
        for pattern in equation_patterns:
            matches = re.findall(pattern, full_text, re.DOTALL)
            equations.extend(matches)

        # Format equations properly
        formatted_equations = []
        for eq in equations:
            eq = eq.strip()
            if eq:  # Only add non-empty equations
                if len(eq) > 15 or re.search(r"\\[a-zA-Z]+", eq):  # Complex equation
                    formatted_equations.append(f"$$ {eq} $$")
                else:  # Simple equation
                    formatted_equations.append(f"\\( {eq} \\)")

        # Enhanced table detection
        tables = []
        if (re.search(r"table|chart|data.*show", question_text) or
            re.search(r"\\begin\{tabular\}", item["raw_question"]) or
            re.search(r"\\begin\{array\}", item["raw_question"])):
            # Try to extract actual table if present
            table_match = re.search(r"(\\begin\{tabular\}.*?\\end\{tabular\})",
                                  item["raw_question"], re.DOTALL)
            if table_match:
                tables.append(table_match.group(1))
            else:
                # Generate example table based on context
                if "grade" in question_text or "score" in question_text:
                    tables.append("\\begin{tabular}{c|c} Grade & Score \\\\ \\hline A & 90-100 \\\\ B & 80-89 \\end{tabular}")
                elif "time" in question_text or "hour" in question_text:
                    tables.append("\\begin{tabular}{c|c} Time & Value \\\\ \\hline 1 hr & 60 min \\\\ 2 hr & 120 min \\end{tabular}")
                else:
                    tables.append("\\begin{tabular}{c|c} Category & Value \\\\ \\hline Item 1 & Data 1 \\\\ Item 2 & Data 2 \\end{tabular}")

        # Enhanced figure detection
        if "figures" in item:  # From TeX parsing
            figures = item["figures"]  # Preserve raw TikZ code or reference
        else:
            figures = []
            if (re.search(r"figure|diagram|graph|chart|image", question_text) or
                re.search(r"\\begin\{tikzpicture\}", item["raw_question"]) or
                re.search(r"\\includegraphics", item["raw_question"])):
                # Try to extract actual figure code
                tikz_match = re.search(r"(\\begin\{tikzpicture\}.*?\\end\{tikzpicture\})",
                                     item["raw_question"], re.DOTALL)
                if tikz_match:
                    figures.append(tikz_match.group(1))
                else:
                    # Generate contextual placeholder
                    if "circle" in question_text:
                        figures.append("circle_diagram.png")
                    elif "triangle" in question_text:
                        figures.append("triangle_diagram.png")
                    elif "graph" in question_text:
                        figures.append("coordinate_graph.png")
                    else:
                        figures.append("diagram_placeholder.png")

        # Extract additional metadata
        difficulty = "medium"  # Default
        if (re.search(r"basic|simple|easy", question_text)):
            difficulty = "easy"
        elif (re.search(r"advanced|complex|difficult|challenging", question_text)):
            difficulty = "hard"

        # Extract subject/topic hints
        subject = "general"
        if re.search(r"math|algebra|geometry|calculus|equation", question_text):
            subject = "mathematics"
        elif re.search(r"chemistry|chemical|molecule|atom", question_text):
            subject = "chemistry"
        elif re.search(r"physics|force|energy|motion", question_text):
            subject = "physics"
        elif re.search(r"biology|cell|organism|dna", question_text):
            subject = "biology"
        elif re.search(r"history|historical|century|war", question_text):
            subject = "history"
        elif re.search(r"english|grammar|literature|writing", question_text):
            subject = "english"

        structured.append(
            {
                "question": item["raw_question"],
                "type": q_type,
                "answer": item.get("raw_answer", ""),
                "equations": formatted_equations,
                "tables": tables,
                "figures": figures,
                "difficulty": difficulty,
                "subject": subject,
                "metadata": {
                    "has_math": len(formatted_equations) > 0,
                    "has_table": len(tables) > 0,
                    "has_figure": len(figures) > 0,
                    "word_count": len(item["raw_question"].split())
                }
            }
        )

    with open("outputs/structured.json", "w") as f:
        json.dump(structured, f, indent=4)
    print(f"Explicit Output: Structured {len(structured)} questions into JSON")
    return structured

def _clean_and_group_questions(raw_data):
    """Clean and group raw question data, properly associating questions with answers and choices."""
    cleaned = []

    # Define instruction patterns to filter out
    instruction_patterns = [
        r'answers?\s+to\s+this\s+paper\s+must\s+be\s+written',
        r'time\s+is\s+to\s+be\s+spent\s+in\s+reading',
        r'time\s+given\s+at\s+the\s+head\s+of\s+this\s+paper',
        r'section\s+[A-Z]\s+is\s+compulsory',
        r'attempt\s+any\s+\w+\s+questions\s+from',
        r'attempt\s+all\s+questions\s+from\s+this\s+section',
        r'choose\s+the\s+correct\s+answers?\s+to\s+the\s+questions',
        r'do\s+not\s+copy\s+the\s+questions',
        r'write\s+the\s+correct\s+answers?\s+only',
        r'intended\s+marks\s+for\s+questions',
        r'marks\s+are\s+given\s+in\s+brackets',
        r'you\s+will\s+not\s+be\s+allowed\s+to\s+write',
        r'will\s+not\s+be\s+allowed\s+to\s+write',
        r'given\s+in\s+brackets?\s*\[',
        r'section-[A-Z]\s*\(\d+\s+marks\)',
    ]

    # Process questions to merge related items and filter instructions
    i = 0
    while i < len(raw_data):
        current = raw_data[i]
        question_text = current["raw_question"].strip()

        # Skip if too short or looks like instruction
        if len(question_text) < 8:
            i += 1
            continue

        # Check if this is an instruction
        is_instruction = any(re.search(pattern, question_text.lower())
                           for pattern in instruction_patterns)

        if is_instruction:
            i += 1
            continue

        # Check if this looks like a main question (numbered and ends with blank or question mark)
        if _is_main_question(question_text):
            # This is a main question, look for associated answer choices and solutions
            question_data = _process_main_question(raw_data, i)
            if question_data:
                cleaned.append(question_data["question"])
                i = question_data["next_index"]
            else:
                i += 1
        else:
            # Skip items that are not main questions (answer choices, solutions, etc.)
            i += 1

    return cleaned

def _is_main_question(text):
    """Check if text looks like a main question."""
    text = text.strip()

    # Must start with a number followed by a dot
    if not re.match(r'^\d+\.', text):
        return False

    # Should not be just a single letter answer choice
    if re.match(r'^\d+\.\s*[A-D][\.\)]\s*[^A-D]*$', text):
        return False

    # Should not be a solution/explanation line (check for these keywords anywhere in the text)
    if re.search(r'(solution|explanation|answer)', text.lower()):
        return False

    # Should not be instruction text
    if re.search(r'(will not be allowed|intended marks|given in brackets)', text.lower()):
        return False

    # Main questions should either:
    # 1. End with a blank (fill-in-the-blank questions)
    # 2. End with a question mark
    # 3. Be long enough to be a complete question (>50 chars) and not start with simple statements
    if re.search(r'_+\.?$', text) or text.endswith('?'):
        return True

    # For longer text, check if it looks like a question vs an answer choice
    if len(text) > 50:
        # Should not start with simple declarative statements that are likely answer choices
        if re.match(r'^\d+\.(It|The|A|An|This|That)\s+', text):
            return False
        return True

    return False

def _process_main_question(raw_data, start_index):
    """Process a main question and gather its associated answer choices and solutions."""
    main_question = raw_data[start_index]["raw_question"].strip()
    answer_choices = []
    correct_answer = ""

    i = start_index + 1

    # Look ahead for answer choices and solutions
    while i < len(raw_data) and i < start_index + 10:  # Look at next 10 items max
        current_text = raw_data[i]["raw_question"].strip()

        # Stop if we hit another main question
        if _is_main_question(current_text):
            break

        # Check for answer choice (numbered option)
        choice_match = re.match(r'^\d+\.\s*(.+)$', current_text)
        if choice_match:
            choice_text = choice_match.group(1).strip()

            # Check if this is a solution/explanation
            if re.search(r'(solution|explanation|answer)', choice_text.lower()):
                # Try to extract the actual answer from the solution
                correct_answer = _extract_answer_from_solution(current_text, main_question)
                i += 1
                break
            # Check if this is a reasonable answer choice
            elif (len(choice_text) < 100 and
                  not re.search(r'(solution|explanation)', choice_text.lower()) and
                  len(choice_text) > 2):  # Must have some content
                answer_choices.append(choice_text)
                i += 1
            else:
                # If it doesn't match our criteria, stop looking
                break
        else:
            # If it doesn't match the numbered pattern, stop looking
            break

    # Determine question type and format
    if re.search(r'_+\.?$', main_question):
        # Fill-in-the-blank question (ends with underscores)
        if answer_choices and len(answer_choices) >= 1:
            # Multiple choice fill-in-blank
            formatted_choices = [f"({chr(65+j)}) {choice}" for j, choice in enumerate(answer_choices)]
            formatted_question = main_question + "\n" + "\n".join(formatted_choices)
            return {
                "question": {"raw_question": formatted_question, "raw_answer": correct_answer},
                "next_index": i
            }
        else:
            # Simple fill-in-blank
            return {
                "question": {"raw_question": main_question, "raw_answer": correct_answer},
                "next_index": i
            }
    else:
        # Regular question
        if answer_choices and len(answer_choices) >= 1:
            # Multiple choice question
            formatted_choices = [f"({chr(65+j)}) {choice}" for j, choice in enumerate(answer_choices)]
            formatted_question = main_question + "\n" + "\n".join(formatted_choices)
            return {
                "question": {"raw_question": formatted_question, "raw_answer": correct_answer},
                "next_index": i
            }
        else:
            # Open-ended question
            return {
                "question": {"raw_question": main_question, "raw_answer": correct_answer},
                "next_index": i
            }



def _extract_answer_from_solution(solution_text, question_text):
    """Extract the actual answer from a solution/explanation text."""
    solution_lower = solution_text.lower()
    question_lower = question_text.lower()

    # First, try to find the specific answer pattern based on the question type
    if 'example of' in question_lower:
        # For "example of" questions, look for "is an example of X"
        example_match = re.search(r'is\s+an?\s+example\s+of\s+([^.]+)', solution_lower)
        if example_match:
            answer = example_match.group(1).strip()
            # Clean up the answer
            answer = re.sub(r'\s+', ' ', answer)
            if len(answer) > 2 and len(answer) < 50:
                return answer

    # For fill-in-blank questions, look for direct statements
    if '______' in question_text:
        # Try to find the answer after "Solution" keyword
        solution_match = re.search(r'solution\s+([^.]+)', solution_lower)
        if solution_match:
            potential_answer = solution_match.group(1).strip()
            # Look for key phrases that indicate the answer
            if 'is' in potential_answer:
                is_match = re.search(r'is\s+([^.]+)', potential_answer)
                if is_match:
                    answer = is_match.group(1).strip()
                    if len(answer) > 2 and len(answer) < 50:
                        return answer

    # Common patterns for extracting answers from solutions
    answer_patterns = [
        r'is\s+([a-zA-Z][^.]{2,30})',      # "is answer" - more specific
        r'are\s+([a-zA-Z][^.]{2,30})',     # "are answer" - more specific
        r'undergo\s+an?\s+([^.]+)',        # For chemistry questions about reactions
        r'termed\s+as\s+([^.]+)',          # "termed as answer"
        r'called\s+([^.]+)',               # "called answer"
        r'known\s+as\s+([^.]+)',           # "known as answer"
    ]

    for pattern in answer_patterns:
        match = re.search(pattern, solution_lower)
        if match:
            answer = match.group(1).strip()
            # Clean up the answer
            answer = re.sub(r'\s+', ' ', answer)
            answer = answer.split('.')[0]  # Take first part before period
            if len(answer) > 2 and len(answer) < 50:  # Reasonable answer length
                return answer

    # If no pattern matches, try to extract key phrases
    if 'undergo' in question_lower:
        # For chemistry reaction questions
        reaction_match = re.search(r'undergo\s+an?\s+([^.]+)', solution_lower)
        if reaction_match:
            return reaction_match.group(1).strip()

    # Fallback: look for the first meaningful word after common keywords
    fallback_patterns = [
        r'solution[:\s]+[^.]*?is\s+([a-zA-Z][^.\s]{2,20})',
        r'answer[:\s]+([a-zA-Z][^.\s]{2,20})',
        r'explanation[:\s]+[^.]*?is\s+([a-zA-Z][^.\s]{2,20})',
    ]

    for pattern in fallback_patterns:
        match = re.search(pattern, solution_lower)
        if match:
            answer = match.group(1).strip()
            if len(answer) > 2 and len(answer) < 30:
                return answer

    return ""  # Return empty string if no answer found